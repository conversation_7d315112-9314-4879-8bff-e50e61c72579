<template>
  <view class="show-more">
    <view
      ref="contentRef"
      class="content overflow-hidden transition-all duration-300 ease-in-out relative"
      :class="{
        'line-clamp': !isExpanded && shouldClamp,
        'shadow-overlay': !isExpanded && showShadow
      }"
      :style="contentStyle"
    >
      <slot></slot>
    </view>
    <view
      v-if="showToggle"
      class="toggle flex items-center justify-center py-2.5 text-gray-500 text-sm cursor-pointer"
      @click="toggleExpanded"
    >
      <text class="toggle-text mr-2.5">{{ isExpanded ? hideText : showText }}</text>
      <uni-icons
        :type="isExpanded ? 'arrow-up' : 'arrow-down'"
        size="14"
        color="#999"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, watch } from 'vue'

interface Props {
  // 最大显示行数，超出后可折叠
  maxLines?: number
  // 展开时显示的文本
  showText?: string
  // 收起时显示的文本
  hideText?: string
  // 是否为富文本内容
  isRichText?: boolean
  // 行高，用于更精确的计算
  lineHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxLines: 3,
  showText: '显示更多',
  hideText: '收起',
  isRichText: false,
  lineHeight: 20
})

const isExpanded = ref(false)
const showToggle = ref(false)
const showShadow = ref(false)
const shouldClamp = ref(false)
const contentRef = ref<any>(null)
const actualHeight = ref(0)
const maxHeight = ref(0)

// 计算样式
const contentStyle = computed(() => {
  const style: any = {}

  if (!isExpanded.value && shouldClamp.value && props.maxLines) {
    if (props.isRichText) {
      // 富文本使用固定高度限制
      style.maxHeight = `${props.maxLines * props.lineHeight}px`
    } else {
      // 普通文本使用 line-clamp
      style['-webkit-line-clamp'] = props.maxLines
    }
  }

  return style
})

// 切换展开/收起状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// 检查内容是否超出指定行数
const checkContentOverflow = () => {
  if (!props.maxLines) {
    showToggle.value = false
    shouldClamp.value = false
    return
  }

  nextTick(() => {
    // #ifdef H5
    checkOverflowH5()
    // #endif

    // #ifndef H5
    checkOverflowMiniProgram()
    // #endif
  })
}

// H5平台检测逻辑
const checkOverflowH5 = () => {
  const contentEl = contentRef.value?.$el || contentRef.value
  if (!contentEl) return

  try {
    const computedStyle = window.getComputedStyle(contentEl as Element)
    const currentLineHeight = parseInt(computedStyle.lineHeight) || props.lineHeight

    // 创建临时元素测量完整高度
    const tempEl = (contentEl as HTMLElement).cloneNode(true) as HTMLElement
    tempEl.style.cssText = `
      position: absolute;
      visibility: hidden;
      top: -9999px;
      left: -9999px;
      max-height: none;
      height: auto;
      -webkit-line-clamp: unset;
      display: block;
      width: ${contentEl.offsetWidth}px;
    `

    document.body.appendChild(tempEl)
    const fullHeight = tempEl.scrollHeight
    document.body.removeChild(tempEl)

    // 计算是否溢出
    const maxAllowedHeight = props.maxLines * currentLineHeight
    const isOverflow = fullHeight > maxAllowedHeight

    actualHeight.value = fullHeight
    maxHeight.value = maxAllowedHeight
    shouldClamp.value = isOverflow
    showToggle.value = isOverflow
    showShadow.value = isOverflow && !props.isRichText
  } catch (error) {
    console.warn('ShowMore: H5 overflow check failed', error)
    // 降级处理
    shouldClamp.value = true
    showToggle.value = true
  }
}

// 小程序平台检测逻辑
const checkOverflowMiniProgram = () => {
  const query = uni.createSelectorQuery()

  query.select('.content').fields({
    size: true,
    computedStyle: ['lineHeight', 'fontSize']
  }, (res: any) => {
    if (!res) return

    const currentLineHeight = res.lineHeight ? parseInt(res.lineHeight) : props.lineHeight
    const height = res.height || 0

    // 计算是否溢出
    const maxAllowedHeight = props.maxLines * currentLineHeight
    const isOverflow = height > maxAllowedHeight

    actualHeight.value = height
    maxHeight.value = maxAllowedHeight
    shouldClamp.value = isOverflow
    showToggle.value = isOverflow
    showShadow.value = isOverflow && !props.isRichText
  }).exec()
}

// 监听内容变化，重新计算是否需要显示展开按钮
onMounted(() => {
  checkContentOverflow()
  
  // 延迟检查，确保内容渲染完成
  setTimeout(() => {
    checkContentOverflow()
  }, 100)
  
  // 再延迟一次确保渲染完成
  setTimeout(() => {
    checkContentOverflow()
  }, 500)
})

// 暴露方法给父组件
defineExpose({
  checkContentOverflow
})
</script>

<style scoped lang="scss">
.line-clamp {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.shadow-overlay::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to bottom, transparent, #ffffff);
  pointer-events: none;
}
</style>