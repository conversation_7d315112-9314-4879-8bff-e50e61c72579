<template>
  <view class="show-more">
    <view 
      ref="contentRef"
      class="content overflow-hidden transition-all duration-300 ease-in-out relative"
      :class="{ 
        'line-clamp': !isExpanded && maxLines,
        'shadow-overlay': !isExpanded && showShadow
      }"
      :style="{ 
        '-webkit-line-clamp': maxLines && !isExpanded ? maxLines : 'unset'
      }"
    >
      <slot></slot>
    </view>
    <view 
      v-if="showToggle" 
      class="toggle flex items-center justify-center py-2.5 text-gray-500 text-sm cursor-pointer"
      @click="toggleExpanded"
    >
      <text class="toggle-text mr-2.5">{{ isExpanded ? hideText : showText }}</text>
      <uni-icons 
        :type="isExpanded ? 'arrow-up' : 'arrow-down'" 
        size="14" 
        color="#999"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'

interface Props {
  // 最大显示行数，超出后可折叠
  maxLines?: number
  // 展开时显示的文本
  showText?: string
  // 收起时显示的文本
  hideText?: string
}

const props = withDefaults(defineProps<Props>(), {
  maxLines: 3,
  showText: '显示更多',
  hideText: '收起'
})

const isExpanded = ref(false)
const showToggle = ref(false)
const showShadow = ref(false)
const contentRef = ref<any>(null)

// 切换展开/收起状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// 检查内容是否超出指定行数
const checkContentOverflow = () => {
  if (!props.maxLines) {
    showToggle.value = false
    return
  }

  nextTick(() => {
    // #ifdef H5
    // H5平台使用document API
    const contentEl = contentRef.value?.$el || contentRef.value || document.querySelector('.content')
    if (contentEl) {
      const computedStyle = window.getComputedStyle(contentEl as Element)
      const lineHeight = parseInt(computedStyle.lineHeight) || 20
      
      // 创建一个临时元素来测量实际内容高度
      const tempEl = (contentEl as HTMLElement).cloneNode(true) as HTMLElement
      tempEl.style.maxHeight = 'none'
      tempEl.style.webkitLineClamp = 'unset'
      tempEl.style.display = 'block'
      tempEl.style.height = 'auto'
      tempEl.style.position = 'absolute'
      tempEl.style.visibility = 'hidden'
      tempEl.style.top = '-9999px'
      
      document.body.appendChild(tempEl)
      const fullHeight = tempEl.scrollHeight
      document.body.removeChild(tempEl)
      
      // 计算实际行数
      const actualLines = Math.ceil(fullHeight / lineHeight)
      const isOverflow = actualLines > props.maxLines
      
      showToggle.value = isOverflow
      showShadow.value = isOverflow
    }
    // #endif
    
    // #ifndef H5
    // 小程序等平台使用uni.createSelectorQuery
    const query = uni.createSelectorQuery()
    
    // 获取line-height和高度信息
    query.select('.content').fields({
      computedStyle: ['lineHeight'],
      size: true
    }, (res: any) => {
      if (res) {
        const lineHeight = res.lineHeight ? parseInt(res.lineHeight) : 20
        const height = res.height || 0
        
        // 通过比较高度判断是否溢出
        const currentLines = Math.ceil(height / lineHeight)
        const isOverflow = currentLines >= props.maxLines
        
        showToggle.value = isOverflow
        showShadow.value = isOverflow
      }
    }).exec()
    // #endif
  })
}

// 监听内容变化，重新计算是否需要显示展开按钮
onMounted(() => {
  checkContentOverflow()
  
  // 延迟检查，确保内容渲染完成
  setTimeout(() => {
    checkContentOverflow()
  }, 100)
  
  // 再延迟一次确保渲染完成
  setTimeout(() => {
    checkContentOverflow()
  }, 500)
})

// 暴露方法给父组件
defineExpose({
  checkContentOverflow
})
</script>

<style scoped lang="scss">
.line-clamp {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.shadow-overlay::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to bottom, transparent, #ffffff);
  pointer-events: none;
}
</style>