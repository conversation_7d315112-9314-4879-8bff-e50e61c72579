<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "style": {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    "navigationStyle": "custom",
    "navigationBarTitleText": "首页"
  }
}
</route>

<script lang="ts" setup>
import { useThemeStore } from '@/store'

defineOptions({
  name: 'Home',
})

const themeStore = useThemeStore()

// 获取屏幕边界到安全区域距离
let safeAreaInsets
let systemInfo

// #ifdef MP-WEIXIN
// 微信小程序使用新的API
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif
</script>

<template>
  <view class="bg-white px-4 pt-2" :style="{ marginTop: `${safeAreaInsets?.top}px` }">
    <view class="mt-10">
      <image src="/static/logo.svg" alt="" class="mx-auto block h-28 w-28" />
    </view>
    <view class="mt-4 text-center text-4xl text-[#d14328]">
      欢迎使用
    </view>
    <view class="mb-8 mt-2 text-center text-2xl">
      您的应用
    </view>

    <view class="m-auto mb-2 max-w-100 text-center text-4">
      这是一个基于 uni-app 的应用，您可以开始构建您的功能。
    </view>

    <view class="mt-4 text-center">
      <wd-button type="primary" class="ml-2" @click="themeStore.setThemeVars({ colorTheme: '#37c2bc' })">
        设置主题变量
      </wd-button>
    </view>
    <view class="h-6" />
  </view>
</template>
