# ShowMore 组件使用说明

## 基本用法

```vue
<template>
  <show-more :max-lines="3">
    <text>这是一段很长的文本内容，当超过3行时会自动折叠，点击按钮可以展开或收起。</text>
  </show-more>
</template>

<script setup>
import ShowMore from '@/components/showMore/index.vue'
</script>
```

## 富文本内容用法

组件会自动处理富文本内容，无需特殊配置：

```vue
<template>
  <show-more ref="showMoreRef" :max-lines="3">
    <rich-text :nodes="richTextContent"></rich-text>
  </show-more>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ShowMore from '@/components/showMore/index.vue'

const showMoreRef = ref(null)
const richTextContent = ref('')

onMounted(() => {
  // 模拟获取富文本内容
  richTextContent.value = `<p>这是一段富文本内容，包含<strong>粗体文字</strong>和<i>斜体文字</i>。</p>
  <p>第二段内容，用来测试多行文本的折叠效果。</p>
  <p>第三段内容，继续增加文本长度。</p>
  <p>第四段内容，确保超过指定的行数限制。</p>
  <p>第五段内容，最终测试折叠功能是否正常工作。</p>`
})
</script>
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| maxLines | Number | 3 | 最大显示行数，超出后可折叠 |
| showText | String | '显示更多' | 展开时显示的文本 |
| hideText | String | '收起' | 收起时显示的文本 |

## 方法说明

| 方法名 | 说明 |
| --- | --- |
| checkContentOverflow() | 手动检查内容是否超出指定行数 |

## 特性说明

1. **自动检测内容溢出**：组件会自动检测内容是否超出指定行数，如果超出则显示"显示更多"按钮
2. **阴影效果**：当内容被折叠时，底部会显示渐变阴影效果，提升用户体验
3. **跨平台兼容**：支持H5、微信小程序等多个平台
4. **多种内容支持**：支持普通文本、富文本等各种内容类型
5. **自动适配**：无需区分内容类型，组件会自动处理

## 注意事项

1. 组件会自动检测内容是否超出指定行数，对于富文本内容会在渲染完成后自动检测
2. 如果需要手动触发检测（比如动态更新内容后），可以调用 `checkContentOverflow()` 方法
3. 组件适用于普通文本、富文本等各种内容类型
4. 当内容被折叠时，底部会显示渐变阴影效果，展开后阴影消失